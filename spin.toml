spin_manifest_version = 2

[application]
authors = [""]
description = ""
name = "draw_draw"
version = "0.1.0"

[variables]
environment = { default = "loc" }
enable_debug = { default = "false" }
pgsql = { default = "" }
pgsql_database = { default = "" }
db_username = { default = "" }
db_password = { default = "" }

[[trigger.http]]
route = "/payment/..."
component = "payment-service"

[component.payment-service.variables]
environment = "{{ environment }}"
enable_debug = "{{ enable_debug }}"
pgsql = "{{ pgsql }}"
pgsql_database = "{{ pgsql_database }}"
db_username = "{{ db_username }}"
db_password = "{{ db_password }}"

[component.payment-service]
source = "PaymentService/dist/payment-service.wasm"
allowed_outbound_hosts = []

[component.payment-service.build]
command = "npm install && npm run build"
workdir = "PaymentService"

[[trigger.http]]
route = "/user/..."
component = "user-service"

[component.user-service.variables]
environment = "{{ environment }}"
enable_debug = "{{ enable_debug }}"
pgsql = "{{ pgsql }}"
pgsql_database = "{{ pgsql_database }}"
db_username = "{{ db_username }}"
db_password = "{{ db_password }}"

[component.user-service]
source = "UserService/dist/user-service.wasm"
allowed_outbound_hosts = []

[component.user-service.build]
command = "npm install && npm run build"
workdir = "UserService"