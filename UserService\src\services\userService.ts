import type { User } from '../models/user';
import * as userRepo from '../repositories/userRepository';

export async function listUsers(): Promise<User[]> {
    return userRepo.getAllUsers();
}

export async function getUser(id: string): Promise<User | undefined> {
    return userRepo.getUserById(id);
}

export async function addUser(data: User): Promise<User> {
    // 可加入業務邏輯驗證等
    return userRepo.createUser(data);
}

export async function modifyUser(id: string, data: Partial<User>): Promise<User | undefined> {
    return userRepo.updateUser(id, data);
}

export async function removeUser(id: string): Promise<boolean> {
    return userRepo.deleteUser(id);
}
