import { Route } from '../../../sharedUtils/src/router';
import * as userService from '../services/userService';
import * as authService from '../../../SharedUtils/src/firebase/auth';
import { get } from '@spinframework/spin-variables';

export const userRoutes: Route[] = [
    {
        method: 'GET',
        path: '/user',
        handler: async () => {
            const users = await userService.listUsers();
            return new Response(JSON.stringify(users), {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
            });
        },
    },
    {
        method: 'POST',
        path: '/user',
        handler: async (req) => {
            const data = await req.json();
            const created = await userService.addUser(data);
            return new Response(JSON.stringify({ message: 'User created', data: created }), {
                status: 201,
                headers: { 'Content-Type': 'application/json' },
            });
        },
    },
    {
        method: 'GET',
        path: '/user/:id',
        handler: async (_req, { id }) => {
            // const user = await userService.getUser(id);
            const user = await authService.FirebaseUserLogin();
            if (!user) return new Response('User Not Found', { status: 404 });
            return new Response(JSON.stringify(user), {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
            });
        },
    },
];
