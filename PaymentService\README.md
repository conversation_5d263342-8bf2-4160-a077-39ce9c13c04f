# `http-ts` Template

A starter template for building TypeScript HTTP applications with Spin.

## Getting Started

Build the App

```bash
spin build
```

## Run the App 

```bash
spin up
```

## Using Spin Interfaces

To use additional Spin interfaces, install the corresponding packages:

| Interface     | Package                         |
|---------------|---------------------------------|
| Key-Value     | `@spinframework/spin-kv`        |
| LLM           | `@spinframework/spin-llm`       |
| MQTT          | `@spinframework/spin-mqtt`      |
| MySQL         | `@spinframework/spin-mysql`     |
| PostgreSQL    | `@spinframework/spin-postgres`  |
| Redis         | `@spinframework/spin-redis`     |
| SQLite        | `@spinframework/spin-sqlite`    |
| Variables     | `@spinframework/spin-variables` |