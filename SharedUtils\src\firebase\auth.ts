import { initializeApp, getApps } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';

var firebaseConfig = {
    apiKey: "AIzaSyAq_I9wECbPnJkmb9Rtom26iKnlei_SAb8",
    authDomain: "drawdraw-df6ea.firebaseapp.com",
    // The value of `databaseURL` depends on the location of the database
    // databaseURL: "https://DATABASE_NAME.firebaseio.com",
    projectId: "drawdraw-df6ea",
    // The value of `storageBucket` depends on when you provisioned your default bucket (learn more)
    // storageBucket: "PROJECT_ID.firebasestorage.app",
    // messagingSenderId: "SENDER_ID",
};

// if (!getApps().length) {
//     initializeApp(firebaseConfig);
// }
function initializeFirebase() {
    if (!getApps().length) {
        initializeApp(firebaseConfig);
    }
}

export async function FirebaseUserLogin(): Promise<string> {
    initializeFirebase();
    // const header = request.headers.get('Authorization') || '';
    // const match = header.match(/^Bearer\s+(.+)$/);
    // if (!match) throw new Error('No Firebase ID Token provided');
    const auth = getAuth();
    var userCredential = await signInWithEmailAndPassword(auth, "<EMAIL>", "Aa123456789");
    return userCredential.user.uid;
}
