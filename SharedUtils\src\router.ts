type Handler = (request: Request, params: Record<string, string>) => Promise<Response>;

export interface Route {
    method: string;
    path: string;
    handler: Handler;
}

export function matchRoute(
    routes: Route[],
    method: string,
    path: string
): { handler: Handler; params: Record<string, string> } | null {
    // Pre-split the request path to avoid repeated work
    const pathParts = path.split('/').filter(Boolean);
    
    for (const route of routes) {
        // Skip if HTTP method does not match
        if (route.method !== method) continue;
        
        // Split the route path and compare lengths
        const routeParts = route.path.split('/').filter(Boolean);
        if (routeParts.length !== pathParts.length) continue;
        
        const params: Record<string, string> = {};
        let matched = true;
        
        // Inline matching logic for performance
        for (let i = 0; i < routeParts.length && matched; i++) {
            const routePart = routeParts[i];
            const pathPart = pathParts[i];
            
            if (routePart.startsWith(':')) {
                // Extract and decode dynamic parameter
                params[routePart.slice(1)] = decodeURIComponent(pathPart);
            } else if (routePart !== pathPart) {
                matched = false;
            }
        }
        
        if (matched) {
            return { handler: route.handler, params };
        }
    }
    return null;
}
