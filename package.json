{"name": "draw-draw", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "spin build", "loc": "spin up --variable @config/loc.toml"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"mkdirp": "^3.0.1", "ts-loader": "^9.4.1", "typescript": "^4.8.4", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "dependencies": {"@spinframework/build-tools": "^1.0.1", "@spinframework/wasi-http-proxy": "^1.0.0"}}