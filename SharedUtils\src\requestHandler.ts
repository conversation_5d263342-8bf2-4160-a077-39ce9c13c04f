import type { Route } from './router';
import { matchRoute } from './router';

export function createRequestHandler(routes: Route[]) {
    return async function handle(request: Request): Promise<Response> {
        const url = new URL(request.url);
        // console.log(Variables.get('environment'));
        const matched = matchRoute(routes, request.method, url.pathname);
        return matched
            ? matched.handler(request, matched.params)
            : new Response('Not Found', { status: 404 });
    };
}