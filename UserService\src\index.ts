import { createRequestHandler } from '../../SharedUtils/src/requestHandler';
import { userRoutes } from './handlers/userHandler';

export const handle = createRequestHandler(userRoutes);

// async function handle(request: Request): Promise<Response> {
//         const url = new URL(request.url);
//         return new Response("ok", {
//                 status: 200
//             });
//         // console.log(Variables.get('environment'));
//         // const matched = matchRoute(routes, request.method, url.pathname);
//         // return matched
//         //     ? matched.handler(request, matched.params)
//         //     : new Response('Not Found', { status: 404 });
//     };

//@ts-ignore
addEventListener('fetch', (event: FetchEvent) => {
  // console.log(Variables.get('environment'));
  event.respondWith(handle(event.request));
});



