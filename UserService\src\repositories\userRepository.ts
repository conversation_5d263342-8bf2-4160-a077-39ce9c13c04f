import type { User } from '../models/user';

let users: User[] = [
    { id: '1', name: '<PERSON>' },
    { id: '2', name: '<PERSON>' },
];

export async function getAllUsers(): Promise<User[]> {
    return users;
}

export async function getUserById(id: string): Promise<User | undefined> {
    return users.find((u) => u.id === id);
}

export async function createUser(user: User): Promise<User> {
    users.push(user);
    return user;
}

export async function updateUser(id: string, data: Partial<User>): Promise<User | undefined> {
    const user = users.find((u) => u.id === id);
    if (user) {
        Object.assign(user, data);
    }
    return user;
}

export async function deleteUser(id: string): Promise<boolean> {
    const index = users.findIndex((u) => u.id === id);
    if (index >= 0) {
        users.splice(index, 1);
        return true;
    }
    return false;
}
